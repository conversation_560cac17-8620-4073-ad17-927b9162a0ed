import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import { <PERSON><PERSON>, <PERSON><PERSON>, Body, Close } from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { baseTheme } from '../../../themes/theme';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext'; 
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { useState, useEffect } from 'react';
import { Spinner } from '@zendeskgarden/react-loaders';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin-bottom: 20px;
`;

interface MediaGallery {
  id: number;
  value: string;
  is_disabled?: boolean;
  position?: number;
  url?: string;
  image_tags: any;
  title?: string;
  description?: string;
}

interface MediaDeleteProps {
  refetch: () => void;
  prodId: number | undefined;
  drawer: boolean;
  setDrawer: React.Dispatch<React.SetStateAction<boolean>>;
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const MediaDelete: React.FC<MediaDeleteProps> = ({
  refetch,
  prodId,
  drawer,
  setDrawer,
  visible,
  setVisible,
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const [media_gallery, setMediaGallery] = useState<MediaGallery[]>([]);
  const [isChange, setIsChange] = useState(false);
  const [userData, setUserData] = useState<{
    browser: string;
    os: string;
    device: string;
  }>({ browser: '', os: '', device: '' });

  const {
    contextProdData,
    selectedFileData,
    setSelectedFileData,
    detectBrowser,
  } = useProductContext();

  const { mutate, isLoading } = useMutation(
    async () => {
      const formData = new FormData();
      if (selectedFileData) {
        const metaData = {
          img1: {
            id: selectedFileData.id,
            value: selectedFileData.value,
            isVideo: selectedFileData.url ? true : undefined,
            delete: true,
          },
        };
        const updatedMediaGallery = media_gallery.filter(
          (item) => item.id !== selectedFileData.id,
        );
        const updatedPositions = updatedMediaGallery.map((item, index) => ({
          id: item.id,
          position: index + 1,
          isVideo: item?.url ? true : undefined,
          image_tags:
            item?.url === undefined
              ? item.image_tags
                ? item.image_tags.split(',').map((tag: string) => tag.trim())
                : []
              : undefined,
        }));

        const orderedPositions = updatedPositions.reduce(
          (acc: any, item, index) => {
            acc[`item_${index + 1}`] = item;
            return acc;
          },
          {},
        );
        const mediaData = {
          ...metaData,
          ...orderedPositions,
        };

        formData.append('mediaData', JSON.stringify(mediaData));
      }
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/handle-media/${prodId}`,
        `${constants.CATALOG_URL}/v1/catalog-admin/handle-media/${prodId}`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'multipart/form-data',
            admin_identifier: `${localStorage.getItem('username')}`,
            platform: `${userData.device}`,
            user_agent: `${userData.browser}(${userData.os})`,
          },
        },
      );
      return response.data;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        addToast('error', `${err?.message}`);
        // refetch();
        setVisible(false);
        setDrawer(false);
      },
      onSuccess: () => {
        addToast('success', 'Successfully deleted.');
        refetch();
        setDrawer(false);
        setSelectedFileData(null);
      },
    },
  );

  const handleYes = () => {
    if (selectedFileData) {
      mutate();
    } else {
      addToast('error', 'Please add File!');
    }
  };

  useEffect(() => {
    setMediaGallery(contextProdData?.media_gallery_entries || []);
  }, [contextProdData]);

  useEffect(() => {
    const metaData = detectBrowser();
    setUserData(metaData);
  }, []);

  return (
    <div>
      {visible && (
        <Modal onClose={() => setVisible(false)}>
          <Header
            tag="h2"
            style={{
              display: 'flex',
              color: '#FFF',
              fontSize: '20px',
              backgroundColor: `${baseTheme.colors.deepBlue}`,
              justifyContent: 'center',
            }}
          >
            Delete File
          </Header>
          <Body>
            <Row justifyContent="center">
              <h3>Do you want to Delete file?</h3>
            </Row>
            <Row>
              <Col
                style={{ display: 'flex', justifyContent: 'end', gap: '20px' }}
              >
                <Button onClick={() => setVisible(false)}>NO</Button>
                <Button onClick={handleYes} isPrimary isOrange>
                  {isLoading ? <Spinner /> : 'YES'}
                </Button>
              </Col>
            </Row>
          </Body>
          <Close aria-label="Close modal" style={{ color: 'white' }} />
        </Modal>
      )}
    </div>
  );
};

export default MediaDelete;
