import React, { useState, useEffect } from 'react';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ExportTableColumns as columns } from '../../../components/table/product/Columns';
import {
  TableContainer,
  TableHolder,
} from '../../../components/UI-components/Table';
import NothingToshow from '../../../components/UI-components/NothingToShow';
import { DataTable } from '../../../components/table/product/DataTable';
import { useQuery } from '@tanstack/react-query';
import routes from '../../../constants/routes';
import useAxios from '../../../hooks/useAxios';
import { baseTheme } from '../../../themes/theme';
import { Col, Row } from '../../../components/UI-components/Grid';
import { DownIcon, LeftArrowIcon, RightArrowIcon } from '../../../utils/icons';
import { Button } from '../../../components/UI-components/Button';
import { Pagination } from '../../../components/UI-components/Pagination';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { Span } from '@zendeskgarden/react-typography';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import useToast from '../../../hooks/useToast';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

export interface ExportColumns {
  id: number;
  status: string;
  filename: string;
  report_url: string | null;
  created_at: string;
  updated_at: string;
}

const Export = () => {
  const axios = useAxios();
  const addToast = useToast();
  const [reportData, setReportData] = useState<ExportColumns[]>([]);
  const [count, setCount] = useState(0);
  const [page, setPage] = useState(1);
  const [rotated, setRotated] = useState<boolean | undefined>();

  const { data, error, refetch, isLoading, isRefetching, isFetching } =
    useQuery({
      queryKey: ['get-export-csv-list'],
      queryFn: async (): Promise<any> => {
        try {
          const response = await axios.get(
            // `${krakendPaths.CATALOG_URL}/admin-api/v1/bulk-data-csv-list?page_no=${page}`,
            `${constants.CATALOG_URL}/v1/catalog-admin/bulk-data-csv-list?page_no=${page}`,
            {
              headers: {
                'x-api-key': `${constants.CATALOG_KEY}`,
                'Content-Type': 'application/json',
              },
            },
          );
          return response;
        } catch (error) {
          // console.log('Error fetching product data:', error);
          throw new Error('Failed to fetch product data');
        }
      },
      onError: (err) => {
        // console.log('error', err);
        addToast('error', 'Failed to fetch Export Data.');
      },
      onSuccess: (data) => {
        // console.log('report data: ', data);
        setCount(data.item_count);
        setReportData(data.data);
      },
    });

  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const table = useReactTable({
    data: reportData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setPage(1);
    table.setPageSize(pageSize);
  };

  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'Report',
      breadcrumbParent: '',
    });
    table.setPageSize(20);
  }, []);

  useEffect(() => {
    refetch();
  }, [page]);
  return isLoading || isRefetching || isFetching ? (
    <LazyLoading />
  ) : (
    <div style={{ padding: '10px 20px' }}>
      <TableContainer>
        <TableHolder>
          {table.getRowModel().rows?.length ? (
            <DataTable table={table} columns={columns} data={reportData} />
          ) : (
            <NothingToshow divHeight="55vh" />
          )}
        </TableHolder>
        {table.getRowModel().rows?.length && count > 0 ? (
          <div style={{ overflowX: 'clip' }}>
            <Row
              style={{
                height: `${baseTheme.components.dimension.width.base * 5}px`,
                marginTop: baseTheme.space.sm,
                backgroundColor: baseTheme.colors.white,
                paddingLeft: baseTheme.space.lg,
                paddingRight: baseTheme.space.lg,
              }}
              justifyContent="center"
              alignItems="center"
            >
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="center">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={page <= 1 ? true : false}
                    onClick={() => {
                      setPage(page - 1);
                    }}
                  >
                    <Button.StartIcon>
                      <LeftArrowIcon />
                    </Button.StartIcon>
                    Previous
                  </Button>
                </Row>
              </Col>
              <Col textAlign="center" lg={2} md={2}>
                <Dropdown
                  onSelect={(item) => handleRowPerPage(item)}
                  onStateChange={(options) =>
                    Object.hasOwn(options, 'isOpen') &&
                    setRotated(options.isOpen)
                  }
                >
                  <Trigger>
                    <Button size="medium" isAction>
                      Row Per Page:
                      <Span style={{ paddingLeft: baseTheme.space.sm }}>
                        {table.getState().pagination.pageSize}
                      </Span>
                      <Button.EndIcon
                        isRotated={rotated}
                        style={{ marginLeft: 0 }}
                      >
                        <DownIcon />
                      </Button.EndIcon>
                    </Button>
                  </Trigger>
                  <Menu>
                    <Item value={20}>20</Item>
                    <Item value={50}>50</Item>
                    <Item value={100}>100</Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col lg={5} md={5}>
                <Row justifyContent="center" alignItems="center">
                  <Pagination
                    color={baseTheme.colors.deepBlue}
                    totalPages={Math.ceil(
                      count / table.getState().pagination.pageSize,
                    )}
                    pagePadding={2}
                    currentPage={page}
                    onChange={(e) => {
                      // setFilters((prev) => ({ ...prev, page: e }));
                      setPage(e);
                    }}
                  />
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="end">
                  <Button size="medium" isAction style={{ cursor: 'default' }}>
                    {(page - 1) * table.getState().pagination.pageSize + 1}-
                    {count < page * table.getState().pagination.pageSize
                      ? count
                      : page * table.getState().pagination.pageSize}{' '}
                    of {count}
                  </Button>
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="end" alignItems="end">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={
                      page >=
                      Math.ceil(count / table.getState().pagination.pageSize)
                        ? true
                        : false
                    }
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                    <Button.EndIcon>
                      <RightArrowIcon />
                    </Button.EndIcon>
                  </Button>
                </Row>
              </Col>
            </Row>
          </div>
        ) : (
          <>{''}</>
        )}
      </TableContainer>
    </div>
  );
};

export default Export;
