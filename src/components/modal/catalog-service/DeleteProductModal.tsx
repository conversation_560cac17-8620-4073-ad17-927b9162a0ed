import React, { useState } from 'react';
import { But<PERSON> } from '@zendeskgarden/react-buttons';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Row, Col } from '@zendeskgarden/react-grid';
import { Product } from '../../layouts/catalog-service/category/ProductsInCategory';
import { useMutation } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import { MenuItem } from '../../../pages/catalog-service/CategoryDropDown';
import { Spinner } from '@zendeskgarden/react-loaders';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const DeleteProductModal = ({
  close,
  selectedProduct,
  selectedCategory,
  refetch,
}: {
  close: () => void;
  selectedProduct: Product;
  selectedCategory: MenuItem;
  refetch: () => void;
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { mutate, isLoading } = useMutation(
    async () => {
      const response = await axios.patch(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/products/remove`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category/remove-products`,
        {
          categoryId: selectedCategory.id,
          product_ids: [selectedProduct.id],
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return response;
    },
    {
      onSuccess() {
        addToast('success', 'Deleted Successfully');
        refetch();
        close();
      },
      onError(error) {
        addToast('error', (error as any)?.message);
      },
    },
  );

  return (
    <Modal onClose={close}>
      <Header tag="h2" isDanger>
        Remove product from category
      </Header>
      <Body>Are you sure you want to delete this product ?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={close} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={() => {
              mutate();
            }}
          >
            {isLoading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteProductModal;
