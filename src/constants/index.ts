import { CART_BASE } from '../../env';

export default {
  API_KEY: 'AoOwAuaTloXKwc2FaBPhDyzhuuYZsXCb',
  // BACKEND_URL: 'http://ordermg-staging.dentalkart.com',
  // BACKEND_URL: 'http://localhost:3002',
  SCRAPER_URL: '',
  SCRAPER_API_KEY: '',
  TOKEN:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InN1cGVyYWRtaW4iLCJzdWIiOiIxODZlNGRkZi1hOTExLTRkYjktOTM5OS05MmYzNmM2ODc5MzkiLCJyb2xlIjpbIlNVUEVSX0FETUlOIiwiQURNSU4iLCJyZXR1cm4iLCJzYWxlIiwiY2FsbCIsInJlaW1idXJzZW1lbnQiLCJmb3JtIiwid2hhdHNhcHAiLCJvcmRlciIsInZpbm9yZGVyIiwiYWRtaW4iLCJjYW5jZWxsYXRpb24iXSwiaGF2ZUFsbEFjY2VzcyI6dHJ1ZSwiaWF0IjoxNjg3Njc4MTAwLCJleHAiOjE2ODgyODI5MDB9.hyxr-ksu_VyD1E7f_gC3SKGGA_OGJW0ENR4nF9ZJY-I',
  GRAPHQL_URI: 'https://dental-admin.dentalkart.com/react-admin/gql',
  NPS_URL: 'https://dental-admin.dentalkart.com/customer/survey',
  GRAPHQL_URI_V2: 'https://dental-admin-v2.dentalkart.com/react-admin/gql',
  IMAGE_URI: 'https://images.dentalkart.com/media/catalog/product/',
  CATEGORY_DATA_URL:
    'https://serverless-prod.dentalkart.com/api/v1/category-data',
  PRODUCT_LIST_VIEW_URL:
    'https://serverless-prod.dentalkart.com/api/v1/products/list/view',
  PRODUCT_LIST_VIEW_API_KEY: 'ZFobrRyccnTyXyXHPUVO4eyyKEKoSjWB',
  // GRAPHQL_URI: 'http://localhost:3002/react-admin/gql',
  // NEW_AUTH_URL: 'https://prod-adminapis.dentalkart.com',
  ADMIN_API_GATEWAY: 'https://adminapis.dentalkart.com',
  // RETURN_URL: 'http://localhost:3000',
  // RETURN_URL: 'https://return-staging.dentalkart.com',
  // ADMIN_API_GATEWAY: 'https://staging-adminapis.dentalkart.com',
  RETURN_URL: 'https://return-service-prod.dentalkart.com',
  RETURN_API_KEY: '6150e562c77e6ca994aa36c3164dbbecf0',
  PREFIX: 'admin-v52',
  // CART_STAGING: 'https://gq-cart.dentalkart.com/api/v1/admin',
  CUSTOMER_URL: 'https://customer-service.dentalkart.com',
  CUSTOMER_API_KEY: '8tyeXkUYPg2JiP8AXBTj9DuIuTfO3kO9cm',
  // GRAPHQL_URI: 'http://localhost:3002/graphql',
  // FEEDS_URL: 'https://feeds-prod.dentalkart.com',
  // COUPON_URL: 'https://prod-adinapis.dentalkart.com',
  // COUPON_URL: 'https://prod-coupon-service.dentalkart.com',
  // COUPON_URL: 'http://localhost:3000',
  COUPON_KEY: '&f%a$i#s@l!bKIv0Gu0d(*whfaisal648k',
  // INTERFACE_URL: 'http://localhost:3011',
  // INTERFACE_URL: 'https://interface-prod.dentalkart.com',
  // CATALOG_URL: 'https://catalogservice-dev.dentalkart.com',
  CART_URL: `${CART_BASE}/api/v1/admin`,
  // CART_URL: 'https://gq-cart.dentalkart.com',
  CATALOG_URL: 'https://catalog-service.dentalkart.com',
  // CATALOG_URL: 'https://catalogservice-dev.dentalkart.com',
  // CATALOG_URL: 'http://localhost:3002',
  // CATALOG_URL: 'https://catalog-service.dentalkart.com',
  // INTERFACE_URL: 'http://localhost:3011',
  // INTERFACE_URL: 'https://interface-prod.dentalkart.com',
  // CATALOG_URL: 'https://catalogservice-dev.dentalkart.com',
  // CATALOG_URL: 'https://catalog-service.dentalkart.com',
  CATALOG_KEY: 'dentalkart101',
  // CATALOG_MEDIA_URL: 'https://return-staging.dentalkart.com',
  TINYMCE_API_KEY: 'mjoznn8yh1okfr8tvrvh4pm2vo7zqa6ls2pls0giak9hcrty',
  TINYMCE_TOOLBAR:
    'undo redo blocks bold italic | forecolor bullist numlist alignleft aligncenter alignright alignjustify | link | image| table tabledelete charmap | removeformat | help ',
  TINYMCE_CONTENT_STYLE:
    'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
  ADMIN_BACKEND_URL: 'https://dental-admin.dentalkart.com',
  // ADMIN_BACKEND_URL: 'http://localhost:3002',
};

export interface Module {
  name: string;
  code: string;
  isActive: boolean;
  requiredRoles?: string[];
  description?: string;
  dependencies?: string[];
  krakendPermissions?: string[];
}

export const MODULES: Module[] = [
  {
    name: 'Home-Page',
    code: 'home-page',
    isActive: true,
    description: 'Manage home page',
  },
  {
    name: 'Return',
    code: 'return',
    isActive: true,
    krakendPermissions: [
      'export:order-mg-refund',
      'massupdate:order-mg-refund',
      'rm:order-mg-refund',
      'update:order-mg-refund',
      'create:order-mg-refund',
      'get:order-mg-refund',
      'export:order-mg-refund-payment',
      'massupdate:order-mg-refund-payment',
      'rm:order-mg-refund-payment',
      'update:order-mg-refund-payment',
      'create:order-mg-refund-payment',
      'get:order-mg-refund-payment',
    ],
    description: 'Manage product returns',
  },
  {
    name: 'Sale',
    code: 'sale',
    isActive: true,
    description: 'Manage sales',
  },
  {
    name: 'Call',
    code: 'call',
    isActive: true,
    description: 'Manage calls',
  },
  {
    name: 'Reimbursement',
    code: 'reimbursement',
    isActive: true,
    krakendPermissions: [
      'export:reimbursement',
      'rm:reimbursement',
      'update:reimbursement',
      'create:reimbursement',
      'get:reimbursement',
      'export:reimbursement-configs',
      'rm:reimbursement-configs',
      'update:reimbursement-configs',
      'create:reimbursement-configs',
      'get:reimbursement-configs',
    ],
    description: 'Manage reimbursements',
  },
  {
    name: 'Generic Form',
    code: 'form',
    isActive: true,
    description: 'Manage forms',
  },
  {
    name: 'Whatsapp',
    code: 'whatsapp',
    isActive: true,
    description: 'Manage WhatsApp communications',
  },
  {
    name: 'Order',
    code: 'order',
    isActive: true,
    description: 'Manage orders',
  },
  {
    name: 'Vinorder',
    code: 'vinorder',
    isActive: true,
    description: 'Manage Vinorders',
  },
  {
    name: 'Admin',
    code: 'admin',
    isActive: true,
    description: 'Admin access',
    requiredRoles: ['ADMIN'],
  },
  {
    name: 'Magazine',
    code: 'magazine',
    isActive: true,
    description: 'Manage magazines',
  },
  {
    name: 'Product',
    code: 'product',
    isActive: true,
    description: 'Manage products',
  },
  {
    name: 'Cancellation',
    code: 'cancellation',
    krakendPermissions: [
      'export:cancel-order',
      'massupdate:cancel-order',
      'rm:cancel-order',
      'update:cancel-order',
      'create:cancel-order',
      'get:cancel-order',
    ],
    isActive: true,
    description: 'Manage cancellations',
  },
  {
    name: 'Refund',
    code: 'refund',
    isActive: true,
    description: 'Manage refunds',
    dependencies: ['order'],
  },
  {
    name: 'Feedback',
    code: 'feedback',
    isActive: true,
    krakendPermissions: [
      'export:ctl-product-feedback',
      'massupdate:ctl-product-feedback',
      'rm:ctl-product-feedback',
      'update:ctl-product-feedback',
      'create:ctl-product-feedback',
      'get:ctl-product-feedback',
      'export:ctl-product-suggestion',
      'massupdate:ctl-product-suggestion',
      'rm:ctl-product-suggestion',
      'update:ctl-product-suggestion',
      'create:ctl-product-suggestion',
      'get:ctl-product-suggestion',
    ],
    description: 'Manage feedback',
  },
  {
    name: 'Review Manager',
    code: 'review-manager',
    isActive: true,
    krakendPermissions: [
      'export:review',
      'massupdate:review',
      'rm:review',
      'update:review',
      'create:review',
      'get:review',
      'export:review-customer',
      'massupdate:review-customer',
      'rm:review-customer',
      'update:review-customer',
      'create:review-customer',
      'get:review-customer',
      'export:review-notf',
      'massupdate:review-notf',
      'rm:review-notf',
      'update:review-notf',
      'create:review-notf',
      'get:review-notf',
      'export:review-order',
      'massupdate:review-order',
      'rm:review-order',
      'update:review-order',
      'create:review-order',
      'get:review-order',
      'export:review-product',
      'massupdate:review-product',
      'rm:review-product',
      'update:review-product',
      'create:review-product',
      'get:review-product',
      'export:review-reply',
      'massupdate:review-reply',
      'rm:review-reply',
      'update:review-reply',
      'create:review-reply',
      'get:review-reply',
    ],
    description: 'Manage reviews',
  },
  {
    name: 'Search Product',
    code: 'search-product',
    isActive: true,
    description: 'Search products',
    dependencies: ['product'],
  },
  {
    name: 'Category Rules',
    code: 'rules',
    isActive: true,
    description: 'Manage category rules',
  },
  {
    name: 'DTO Recieved',
    code: 'dto-recieved',
    isActive: true,
    description: 'Manage DTOs',
  },
  {
    name: 'Investor Relation',
    code: 'investor-relation',
    isActive: true,
    description: 'Manage investor relations',
  },
  {
    name: 'Questions',
    code: 'questions',
    isActive: true,
    description: 'Manage questions',
  },
  {
    name: 'FAQ',
    code: 'faq',
    isActive: true,
    description: 'Manage FAQs',
  },
  {
    name: 'Tier Pricing',
    code: 'tierpricing',
    isActive: true,
    krakendPermissions: [
      'export:ctl-product-tierprice',
      'massupdate:ctl-product-tierprice',
      'rm:ctl-product-tierprice',
      'update:ctl-product-tierprice',
      'create:ctl-product-tierprice',
      'get:ctl-product-tierprice',
    ],
    description: 'Manage tier pricing',
  },
  {
    name: 'Feeds',
    code: 'feeds',
    isActive: true,
    description: 'Manage feeds',
  },
  {
    name: 'Coupon',
    code: 'coupon',
    isActive: true,
    description: 'Manage coupons',
  },
  {
    name: 'Delivery',
    code: 'delivery',
    isActive: true,
    description: 'Manage deliveries',
  },
  {
    name: 'Customer',
    code: 'customer',
    isActive: true,
    description: 'Manage Customers',
  },
  {
    name: 'Product',
    code: 'product',
    isActive: true,
    description: 'Manage Products',
  },
  {
    name: 'Supply Chain',
    code: 'supply_chain',
    isActive: true,
    description: 'Manage supply chain',
  },
];

// Helper functions for module management
export const validateModuleSelection = (
  selectedModules: string[],
): { valid: boolean; error?: string } => {
  // Check if modules exist
  const invalidModules = selectedModules.filter(
    (code) => !MODULES.find((m) => m.code === code),
  );
  if (invalidModules.length > 0) {
    return {
      valid: false,
      error: `Invalid modules selected: ${invalidModules.join(', ')}`,
    };
  }

  // Check module dependencies
  for (const moduleCode of selectedModules) {
    const module = MODULES.find((m) => m.code === moduleCode);
    if (module?.dependencies) {
      const missingDependencies = module.dependencies.filter(
        (dep) => !selectedModules.includes(dep),
      );
      if (missingDependencies.length > 0) {
        return {
          valid: false,
          error: `Module "${
            module.name
          }" requires the following modules: ${missingDependencies.join(', ')}`,
        };
      }
    }
  }

  // Check if required modules are active
  const inactiveModules = selectedModules.filter((code) => {
    const module = MODULES.find((m) => m.code === code);
    return module && !module.isActive;
  });
  if (inactiveModules.length > 0) {
    return {
      valid: false,
      error: `The following modules are currently inactive: ${inactiveModules.join(
        ', ',
      )}`,
    };
  }

  return { valid: true };
};

export const getModuleByCode = (code: string): Module | undefined => {
  return MODULES.find((m) => m.code === code);
};

export const getActiveModules = (): Module[] => {
  return MODULES.filter((m) => m.isActive);
};
