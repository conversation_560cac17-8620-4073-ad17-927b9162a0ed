import React, { useEffect, useRef, useState } from 'react';
import { Col, Row } from '../../UI-components/Grid';
import { Button } from '../../UI-components/Button';
import {
  Header,
  Modal,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Checkbox, Field, Label } from '@zendeskgarden/react-forms';
import {
  Item,
  Menu,
  Field as DropField,
  Dropdown,
  Select,
} from '@zendeskgarden/react-dropdowns';
import { baseTheme } from '../../../themes/theme';
import {
  ProductFilters,
  ProductStatus,
  ProductStockStatus,
  ProductTypes,
} from '../../layouts/catalog-service/category/ProductsInCategory';
import useToast from '../../../hooks/useToast';

const ProductInCategoryFilter = ({
  close,
  filters,
  setFilter,
  setCurrentPage
}: {
  close: () => void;
  filters: ProductFilters | undefined;
  setFilter: (filters: ProductFilters) => void;
  setCurrentPage: (page: number) => void;
}) => {
  const ProductStatusOptions: ProductStatus[] = Object.values(ProductStatus);
  const ProductTypeOptions: ProductTypes[] = Object.values(ProductTypes);
  const ProductStockStatusOptions: ProductStockStatus[] =
    Object.values(ProductStockStatus);

  const [localFilters, setlocalFilters] = useState<ProductFilters>({});
  const addToast = useToast();

  useEffect(() => {
    setlocalFilters({...filters});
  }, [filters])


  return (
    <Modal onClose={close}>
      <Header
        style={{
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          color: 'white',
          justifyContent: 'center',
          display: 'flex',
        }}
        tag="h2"
      >
        Filters
      </Header>
      <Body>
        <Row>
          <Col style={{ marginBottom: '60px' }}>
            <Dropdown
              selectedItem={localFilters?.type_id}
              onSelect={(i) => {
                setlocalFilters((prev) => {
                  return {
                    ...prev,
                    type_id: i,
                  } as ProductFilters;
                });
              }}
              downshiftProps={{ itemToString: (item: ProductTypes) => item }}
              
            >
              <DropField >
                <Label>Type</Label>
                <Select >{localFilters?.type_id || "Select Type"}</Select>
              </DropField>
              <Menu>
                {ProductTypeOptions.map((option) => (
                  <Item key={option} value={option}>
                    {option}
                  </Item>
                ))}
              </Menu>
            </Dropdown>
          </Col>
        </Row>
        <Row>
          <Col style={{ marginBottom: '60px' }}>
            <Dropdown
              selectedItem={localFilters?.status}
              onSelect={(i) => {
                setlocalFilters((prev) => {
                  return {
                    ...prev,
                    status: i,
                  } as ProductFilters;
                });
              }}
              downshiftProps={{
                itemToString: (item: ProductStatus) => {
                  return item;
                },
              }}
            >
              <DropField>
                <Label>Status</Label>
                <Select >{localFilters?.status ||"Select Status"}</Select>
              </DropField>
              <Menu>
                {ProductStatusOptions.map((option) => (
                  <Item key={option} value={option}>
                    {option}
                  </Item>
                ))}
              </Menu>
            </Dropdown>
          </Col>
        </Row>
        <Row>
          <Col style={{ marginBottom: '60px' }}>
            <Dropdown
              selectedItem={localFilters?.stock_status}
              onSelect={(i) => {
                setlocalFilters((prev) => {
                  return {
                    ...prev,
                    stock_status: i,
                  } as ProductFilters;
                });
              }}
              downshiftProps={{
                itemToString: (item: ProductStatus) => {
                  return item;
                },
              }}
            >
              <DropField>
                <Label>Stock Status</Label>
                <Select >{localFilters?.stock_status || "Select Stock Status"}</Select>
              </DropField>
              <Menu>
                {ProductStockStatusOptions.map((option) => (
                  <Item key={option} value={option}>
                    {option}
                  </Item>
                ))}
              </Menu>
            </Dropdown>
          </Col>
        </Row>
        <Row>
          <Col>
            <Field>
              <Checkbox
                checked={localFilters?.international_active}
                onChange={() => {
                  setlocalFilters((prev) => {
                    return {
                      ...prev,
                      international_active: !prev?.international_active,
                    } as ProductFilters;
                  });
                }}
                >
                <Label hidden>International</Label>
              </Checkbox>
                <Label>  International</Label>
            </Field>
          </Col>
        </Row>
      </Body>
      <Footer>
        <FooterItem>
          <Button
            isPrimary
            isOrange
            disabled={
              localFilters?.status === undefined &&
              localFilters?.type_id === undefined &&
              localFilters?.stock_status === undefined &&
              localFilters?.international_active === undefined &&
              (localFilters?.name === undefined || localFilters?.name === '')
            }
            onClick={() => {
              setFilter(localFilters as ProductFilters);
              setCurrentPage(1);
              const hasActiveFilters = localFilters && (
                localFilters.status !== undefined ||
                localFilters.type_id !== undefined ||
                localFilters.stock_status !== undefined ||
                localFilters.international_active !== undefined ||
                (localFilters.name !== undefined && localFilters.name !== '')
              );
              
              if (hasActiveFilters) {
                addToast('success', 'Filters applied');
              }
              close();
            }}
          >
            Save
          </Button>
        </FooterItem>
      </Footer>
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default ProductInCategoryFilter;
