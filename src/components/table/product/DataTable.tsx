'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  VisibilityState,
  getPaginationRowModel,
  Table as TTable,
} from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import {
  Body,
  Cell as _Cell,
  Head,
  HeaderCell as _HeaderCell,
  HeaderRow as _HeaderRow,
  Row as _Row,
  Table,
} from '../../UI-components/Table';

import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { UpIcon, DownIcon } from '../../../utils/icons';

const HeaderRow = styled(_HeaderRow)<{ sticky?: boolean }>``;

const HeaderCell = styled(_HeaderCell)<{ sticky?: boolean }>`
  ${(p) => (p.sticky ? 'position: sticky; left : 0;' : '')}

  background-color: ${(p) => p.theme.colors.deepBlue};

  /* Add appropriate left margin and cell width for each sticky column */
  &:nth-child(1) {
    ${(p) => (p.sticky ? 'left: 0; z-index: 499;' : '')}
  }
  &:nth-child(2) {
    ${(p) => (p.sticky ? 'left: 250px;  z-index: 499;' : '')}
  }
  &:nth-child(3) {
    ${(p) => (p.sticky ? 'left: 500px;  z-index: 499;' : '')}
  }
`;

const Cell = styled(_Cell)<{ sticky?: boolean }>`
  /* Add more nth-child rules for additional sticky columns */
  ${(p) => (p.sticky ? 'position: sticky; left: 0;' : '')}

  background-color: ${colors.white};

  /* Add appropriate left margin and cell width for each sticky column */
  &:nth-child(1) {
    ${(p) => (p.sticky ? 'left: 0;' : '')}
  }
  &:nth-child(2) {
    ${(p) => (p.sticky ? 'left: 250px;' : '')}
  }
  &:nth-child(3) {
    ${(p) => (p.sticky ? 'left: 500px;' : '')}
  }
`;

const Row = styled(_Row)``;

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  table: TTable<TData>;
  tableSize?: '"small" | "medium" | "large" | undefined';
  onCancelClick?: (id: number) => void;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  table,
  tableSize,
  onCancelClick,
}: DataTableProps<TData, TValue>) {

  return (
    <>
      <Table size={'large'}>
        <Head isSticky>
          {table.getHeaderGroups().map((headerGroup) => (
            <HeaderRow key={headerGroup.id}>
              {headerGroup.headers.map((header, index) => {
                return (
                  !header.column.columnDef.enableHiding && (
                    <HeaderCell
                      style={{
                        minWidth:
                          index === 0
                            ? baseTheme.components.dimension.width.base100
                            : baseTheme.components.dimension.width.base150,
                        cursor: `${header.column.getCanSort() && 'pointer'}`
                      }}
                      key={header.id}
                      sticky={header.column.columnDef.enablePinning}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      {{asc: <UpIcon style={{marginLeft: "5px"}} />, desc: <DownIcon style={{marginLeft: "5px"}} />}[header.column.getIsSorted() as string]}
                    </HeaderCell>
                  )
                );
              })}
            </HeaderRow>
          ))}
        </Head>
        <Body>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <Row key={row.id} data-state={row.getIsSelected() && 'selected'}>
                {row.getVisibleCells().map(
                  (cell, index) =>
                    !cell.column.columnDef.enableHiding && (
                      <Cell
                        style={{
                          minWidth:
                            index === 0
                              ? baseTheme.components.dimension.width.base100
                              : baseTheme.components.dimension.width.base150,
                        }}
                        sticky={cell.column.columnDef.enablePinning}
                        key={cell.id}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </Cell>
                    ),
                )}
              </Row>
            ))
          ) : (
            <Row>
              <Cell colSpan={columns.length} className="h-24 text-center">
                No results.
              </Cell>
            </Row>
          )}
        </Body>
      </Table>
    </>
  );
}
