import React, { ReactNode, useEffect, useState } from 'react';
import TopBar from '../../topbar/TopBar';
import { ProductColumns, ProductTableColumns as columns } from './Columns';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import styled from 'styled-components';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../UI-components/Table';
import { DataTable } from './DataTable';
import NothingToshow from '../../UI-components/NothingToShow';
import { baseTheme, colors } from '../../../themes/theme';
import { Col, Row } from '../../UI-components/Grid';
import { Button as _Button} from '../../UI-components/Button';
import {
  AddIconWhiteBG,
  CrossIcon,
  DownIcon,
  FilterIcon,
  LeftArrowIcon,
  RefetchIcon,
  RightArrowIcon,
} from '../../../utils/icons';
import { Pagination } from '../../UI-components/Pagination';
import { SM, Span } from '@zendeskgarden/react-typography';
import { useNavigate } from 'react-router-dom';
import { pageRoutes } from '../../navigation/RouteConfig';
import ProductsFilterDrawer from '../../drawer/catalog-service/ProductsFilterDrawer';
import {
  ProductFilterTypes,
  useProductContext,
} from '../../../pages/catalog-service/ProductFilterContext';
import {
  Dropdown,
  Field as _Field,
  Menu,
  Trigger,
  Item,
} from '@zendeskgarden/react-dropdowns';
import GenericCustomTopBar from '../../topbar/GenericCustomTopBar';
import Import from '../../modal/catalog-service/Import';
import ExportDropdown from '../../dropdown/catalog-service/ExportDropdown';
import { Checkbox, Field as CheckField, Fieldset, Label } from '@zendeskgarden/react-forms';
import {
  useScreenDefaultWidth,
  useScreenResSize,
} from '../../../hooks/useScreenSize';
import SearchInput from '../../search-input/SearchInput';

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`;

const DropdownItem = styled.div`
  max-height: 300px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(2, 2fr);
  gap: 10px;
  padding: 20px 15px;
  cursor: pointer;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  gap: 10px;
`;

const Field = styled(CheckField)`
  padding: ${baseTheme.space.sm};
`;

const Button = styled(_Button)`
  ${(p) => (p.theme.colors.primaryHue = baseTheme.colors.deepBlue)}
`

const StyledDropdown = styled(Dropdown)`
  margin-right: 10px;
`;

const ProductsTable = ({
  data,
  searchContent,
  setSearchContent,
  handleSearch,
  filters,
  pageSize,
  setPageSize,
  setFilters,
  count,
  searchIdError,
  setSearchIdError,
  refetch,
  searchType,
  setSearchType,
  currentSearch,
  setCurrentSearch,
}: {
  data: ProductColumns[];
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
  handleSearch: () => void;
  filters: ProductFilterTypes;
  pageSize: number;
  setPageSize: React.Dispatch<React.SetStateAction<number>>;
  setFilters: React.Dispatch<React.SetStateAction<ProductFilterTypes>>;
  count: number;
  searchIdError: string;
  setSearchIdError: React.Dispatch<React.SetStateAction<string>>;
  refetch: any;
  searchType: string;
  setSearchType: React.Dispatch<React.SetStateAction<string>>;
  currentSearch?: string;
  setCurrentSearch?: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rotated, setRotated] = useState<boolean | undefined>();
  const navigate = useNavigate();
  const [isChecked, setIsChecked] = useState(false);
  const {
    contextProdData,
    setContextProdData,
    page,
    setPage,
    statusIds,
    setStatusIds,
  } = useProductContext();
  const from = 'products'
  const [alreadyEnabledColumn, setAlreadyEnabledColumn] = useState<string[]>([
    'Id',
    'Thumbnail',
    'Product Name',
    'Product Type',
    'Quantity',
    'SKU',
    'Backorder',
    'Visibility',
    'Manufacturer',
    'Reward Coin',
    'Product Expiry Date',
    'Status',
    'Price',
    'Gtin',
    'Demo Available',
    'Stock Status',
    'Categories',
    'Completion Percentage',
  ]);

  const disabledColumn = ['Action', 'Id', 'SKU', 'Product Name', 'Product Type', 'Visibility', 'Status'];
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });
  const [isBulkOpen, setIsBulkOpen] = useState(false);
  const [isImport, setIsImport] = useState(false);
  const [isExport, setIsExport] = useState(false);
  const handleToggleDropdown = () => {
    setIsBulkOpen(!isBulkOpen);
  };
  const [rotatedType, setRotatedType] = useState<boolean>();

  const isSmallScreen = useScreenDefaultWidth();

  const handleImportClick = () => {
    setIsImport(true);
    setIsExport(false);
    setIsBulkOpen(false);
  };

  const handleExportClick = () => {
    setIsExport(true);
    setIsImport(false);
    setIsBulkOpen(false);
  };

  const handleCheckboxChange = (event: any, column: any, header: string) => {
    const { checked } = event.target;
    setIsChecked(checked);
    if (checked === true) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) => [...prev, header]);
      const newColumns = alreadyEnabledColumn && [
        ...alreadyEnabledColumn,
        header,
      ];
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(true);
    } else if (checked === false) {
      setAlreadyEnabledColumn &&
        setAlreadyEnabledColumn((prev) =>
          prev.filter((item) => item !== header),
        );
      const newColumns =
        alreadyEnabledColumn &&
        alreadyEnabledColumn.filter((item) => item !== header);
      localStorage.setItem(`${from}`, `${newColumns}`);
      column.toggleVisibility(false);
    }
  };

  // useEffect(()=>{
  //   console.log("alreadyEnabledColumn: ", alreadyEnabledColumn)
  // },[alreadyEnabledColumn])


  const handleRefetch = () => {
    table.toggleAllPageRowsSelected(false);
    refetch();
  };

  const reset = () => {
    setPage(1);
    setSearchContent('');
    setCurrentSearch && setCurrentSearch('');
  };
  const addProduct = () => {
    navigate(`${pageRoutes['GO_TO_ADD_PRODUCT']}/`);
  };
  const handleRowPerPage = (item: string) => {
    const pageSize = Number(item);
    setPageSize(pageSize);
    table.setPageSize(pageSize);
  };
  useEffect(() => {
    table.setPageSize(Number(pageSize));
  }, []);

  const searchOptions = [
    {
      label: 'ID',
      value: 'id',
    },
    {
      label: 'Name',
      value: 'name',
    },
    {
      label: 'SKU',
      value: 'sku',
    },
  ];

  return (
    <div>
      <TopBarDiv>
        <Row>
          <Col size={6}>
            <Row justifyContent="between" alignItems="center">
              <Col>
                <Row alignItems="center">
                  <Col size={6.5}>
                    <Row>
                      <SearchInput
                        isLabelHidden
                        searchContent={searchContent}
                        setSearchContent={setSearchContent}
                        placeholder="Search by keyword"
                        handleSearch={handleSearch}
                        label=""
                        width="100%"
                      />
                    </Row>
                    <Row>
                      {searchIdError !== '' && (
                        <div style={{ color: 'red' }}>
                          {' '}
                          Wrong Keyword for id: {searchIdError}
                        </div>
                      )}
                    </Row>
                  </Col>
                  {searchOptions && (
                    <Col offset={0.5} size={2}>
                      <>
                        <Dropdown
                          onSelect={(item) => setSearchType(item)}
                          onStateChange={(options) =>
                            Object.hasOwn(options, 'isOpen') &&
                            setRotatedType(options.isOpen)
                          }
                        >
                          <Trigger>
                            <Button
                              style={{
                                transform: isSmallScreen
                                  ? 'translateX(-30px)'
                                  : 'translateX(0)',
                              }}
                              isPrimary
                            >
                              {searchOptions.map((option: any) => {
                                return option.value === searchType
                                  ? option.label
                                  : null;
                              })}
                              <Button.EndIcon isRotated={rotatedType}>
                                <DownIcon
                                  style={{
                                    height: baseTheme.iconSizes.md,
                                    width: baseTheme.iconSizes.md,
                                  }}
                                />
                              </Button.EndIcon>
                            </Button>
                          </Trigger>
                          <Menu
                            style={{
                              width:
                                baseTheme.components.dimension.width.base200,
                              transform: 'translateX(4px)',
                              borderRadius: baseTheme.borderRadii.lg,
                            }}
                          >
                            {searchOptions.map((option: any) => (
                              <Item key={option.value} value={option.value}>
                                {option.label}
                              </Item>
                            ))}
                          </Menu>
                        </Dropdown>
                      </>
                    </Col>
                  )}
                </Row>
              </Col>
            </Row>
          </Col>
          <Col
            style={{
              display: 'flex',
              gap: '5px',
              justifyContent: 'end',
              alignItems: 'center',
            }}
          >
            <ActionButtonsContainer>
              <StyledDropdown
                onSelect={(item) => alert(`You planted a ${item}`)}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
                }
              >
                <Trigger>
                  <Button size="medium" isAction onClick={handleToggleDropdown}>Bulk Action</Button>
                </Trigger>
                <Menu
                  style={{
                    width: baseTheme.components.dimension.width.base150,
                    transform: 'translateX(4px)',
                    borderRadius: baseTheme.borderRadii.lg,
                  }}
                >
                  <DropdownItem onClick={handleImportClick}>
                    Import
                  </DropdownItem>
                  <DropdownItem onClick={handleExportClick}>
                    Export
                  </DropdownItem>
                </Menu>
              </StyledDropdown>
              {isImport && (
                <Import visible={isImport} setVisible={setIsImport} />
              )}
              {isExport && (  
                <ExportDropdown  isExport={isExport} setIsExport={setIsExport} /> 
              )}
            </ActionButtonsContainer>
            <>
              <Button
                size="medium"
                isAction
                isPrimary
                onClick={() => {
                  setIsOpen(true);
                }}
              >
                <Button.StartIcon>
                  <FilterIcon />
                </Button.StartIcon>
                <Span>Filter</Span>
              </Button>
            </>
            <>
              <Button size="medium" isAction onClick={handleRefetch}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
            </>

            <StyledDropdown>
              <Dropdown
                onSelect={(item) => alert(`You planted a ${item}`)}
                onStateChange={(options) =>
                  Object.hasOwn(options, 'isOpen') && setRotated(options.isOpen)
                }
              >
                <Trigger>
                  <Button isPrimary>
                    Select Item
                    <Button.EndIcon isRotated={rotated}>
                      <DownIcon
                        style={{
                          height: baseTheme.iconSizes.md,
                          width: baseTheme.iconSizes.md,
                        }}
                      />
                    </Button.EndIcon>
                  </Button>
                </Trigger>
                <Menu
                  style={{
                    width: baseTheme.components.dimension.width.base200,
                    transform: 'translateX(4px)',
                    borderRadius: baseTheme.borderRadii.lg,
                    padding: `${baseTheme.paddings.md} ${baseTheme.paddings.md}`,
                  }}
                >
                  <Fieldset>
                    {table
                      .getAllColumns()
                      .filter(
                        (column) =>
                          column.getCanHide() && column.columnDef.header,
                      )
                      .map((column : any, index) => {
                        if (index === 0) {
                          return null;
                        }
                        const columnHeader =
                          typeof column.columnDef.header === 'function'
                            ? column.columnDef?.accessorKey
                            : (column.columnDef.header as string);

                        // console.log("column: ",column.columnDef)
                        return (
                          <>
                            {!column.columnDef.enableHiding &&
                              !column.columnDef.enableColumnFilter && (
                                <Field key={index}>
                                  <Checkbox
                                    disabled={disabledColumn?.includes(
                                      columnHeader,
                                    )}
                                    key={column.id}
                                    checked={alreadyEnabledColumn?.includes(
                                      columnHeader,
                                    )}
                                    onChange={(e) =>
                                      handleCheckboxChange(
                                        e,
                                        column,
                                        columnHeader,
                                      )
                                    }
                                  >
                                    <Label>{columnHeader}</Label>
                                  </Checkbox>
                                </Field>
                              )}
                          </>
                        );
                      })}
                  </Fieldset>
                </Menu>
              </Dropdown>
            </StyledDropdown>
            <>
              <Button
                isPrimary
                onClick={() => {
                  addProduct();
                }}
              >
                <Button.StartIcon>
                  <AddIconWhiteBG />
                </Button.StartIcon>
                Add New Product
              </Button>
            </>
          </Col>
        </Row>
        <Row justifyContent="end">
          {currentSearch && currentSearch != '' && (
            <SM
              style={{
                marginLeft: baseTheme.space.md,
                marginRight: baseTheme.space.md,
                marginTop: baseTheme.space.xs,
              }}
            >
              You have searched for "{currentSearch}"
            </SM>
          )}
        </Row>
      </TopBarDiv>

      <Container>
        <TableContainer>
          <TableHolder>
            {table.getRowModel().rows?.length ? (
              
              <DataTable table={table} columns={columns} data={data} />
            ) : (
              <NothingToshow divHeight="55vh" />
            )}
          </TableHolder>
          {table.getRowModel().rows?.length && count > 0 ? (
            <div style={{ overflowX: 'clip' }}>
              <Row
                style={{
                  height: `${baseTheme.components.dimension.width.base * 5}px`,
                  marginTop: baseTheme.space.sm,
                  backgroundColor: baseTheme.colors.white,
                  paddingLeft: baseTheme.space.lg,
                  paddingRight: baseTheme.space.lg,
                }}
                justifyContent="center"
                alignItems="center"
              >
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="start" alignItems="center">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      disabled={page <= 1 ? true : false}
                      onClick={() => {
                        setPage(page - 1);
                      }}
                    >
                      <Button.StartIcon>
                        <LeftArrowIcon />
                      </Button.StartIcon>
                      Previous
                    </Button>
                  </Row>
                </Col>
                <Col textAlign="center" lg={2} md={2}>
                  <Dropdown
                    onSelect={(item) => handleRowPerPage(item)}
                    onStateChange={(options) =>
                      Object.hasOwn(options, 'isOpen') &&
                      setRotated(options.isOpen)
                    }
                  >
                    <Trigger>
                      <Button size="medium" isAction>
                        Row Per Page:
                        <Span style={{ paddingLeft: baseTheme.space.sm }}>
                          {table.getState().pagination.pageSize}
                        </Span>
                        <Button.EndIcon
                          isRotated={rotated}
                          style={{ marginLeft: 0 }}
                        >
                          <DownIcon />
                        </Button.EndIcon>
                      </Button>
                    </Trigger>
                    <Menu>
                      <Item value={20}>20</Item>
                      <Item value={50}>50</Item>
                      <Item value={100}>100</Item>
                    </Menu>
                  </Dropdown>
                </Col>
                <Col lg={5} md={5}>
                  <Row justifyContent="center" alignItems="center">
                    <Pagination
                      color={baseTheme.colors.deepBlue}
                      totalPages={Math.ceil(
                        count / table.getState().pagination.pageSize,
                      )}
                      pagePadding={2}
                      currentPage={page}
                      onChange={(e) => {
                        setFilters((prev) => ({ ...prev, page: e }));
                        setPage(e);
                      }}
                    />
                  </Row>
                </Col>
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="start" alignItems="end">
                    <Button
                      size="medium"
                      isAction
                      style={{ cursor: 'default' }}
                    >
                      {(page - 1) * table.getState().pagination.pageSize + 1}-
                      {count < page * table.getState().pagination.pageSize
                        ? count
                        : page * table.getState().pagination.pageSize}{' '}
                      of {count}
                    </Button>
                  </Row>
                </Col>
                <Col lg={1.5} md={1.5}>
                  <Row justifyContent="end" alignItems="end">
                    <Button
                      style={{
                        maxWidth: baseTheme.components.dimension.width.base100,
                      }}
                      size="medium"
                      isAction
                      disabled={
                        page >=
                        Math.ceil(count / table.getState().pagination.pageSize)
                          ? true
                          : false
                      }
                      onClick={() => setPage(page + 1)}
                    >
                      Next
                      <Button.EndIcon>
                        <RightArrowIcon />
                      </Button.EndIcon>
                    </Button>
                  </Row>
                </Col>
              </Row>
            </div>
          ) : (
            <>{''}</>
          )}
        </TableContainer>
      </Container>
      <ProductsFilterDrawer
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        setFilters={setFilters}
        filters={filters}
        reset={reset} isreset={false} setIsReset={function (value: React.SetStateAction<boolean>): void {
          throw new Error('Function not implemented.');
        } } setIsFilterDisabled={function (value: React.SetStateAction<{ count: number; isdisabled: boolean; }>): void {
          throw new Error('Function not implemented.');
        } }        
      />
    </div>
  );
};

export default ProductsTable;
