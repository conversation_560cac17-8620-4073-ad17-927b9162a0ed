import React, { ChangeEvent, useEffect, useState, KeyboardEvent } from 'react';
import { Col, Row } from '../UI-components/Grid';
import {
  Field as _Field,
  Label,
  MediaInput as _MediaInput,
} from '@zendeskgarden/react-forms';
import { SearchIcon } from '../../utils/icons';
import styled from 'styled-components';
import { baseTheme, colors } from '../../themes/theme';
import { IconButton } from '../UI-components/IconButton';

interface FieldProps {
  width?: string;
}

const MediaInput = styled(_MediaInput)`
  color: ${colors.black};

  input::placeholder {
    color: ${colors.apticaGrey};
  }
`;

const Field = styled(_Field)<FieldProps>`
  ${(p) => (p.width ? `width: ${p.width}` : 'width : auto')};
  div {
    padding-top: ${(p) => p.theme.space.sm};
    padding-bottom: ${(p) => p.theme.space.sm};
    background-color: ${colors.bgGrey};
    border: ${baseTheme.borders.sm} ${colors.darkBoredGrey};
    border-radius: ${baseTheme.borderRadii.md};
  }
`;

const SearchInput = ({
  isLabelHidden,
  searchContent,
  setSearchContent,
  placeholder,
  label,
  onSearchClick,
  setData,
  handleSearch,
  width,
}: {
  isLabelHidden?: boolean;
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<undefined | string>>;
  placeholder?: string;
  label?: string;
  onSearchClick?: () => void;
  setData?: React.Dispatch<React.SetStateAction<undefined | string>>;
  handleSearch?: any;
  width?: string;
}) => {
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchContent(inputValue);
  };
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch && handleSearch();
    }
  };
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    // Prevent the default paste behavior
    e.preventDefault();

    // Get the pasted text from the clipboard
    const pastedText = e.clipboardData.getData('text');

    // Set the input value to the pasted text
    setSearchContent(pastedText);

    // Trigger the search
    handleSearch && handleSearch();
  };

  return (
    <>
      <Field width={width}>
        <Label hidden={isLabelHidden}>{label != '' ? label : 'Label'}</Label>
        <MediaInput
          value={searchContent || ''}
          placeholder={placeholder}
          onChange={(e) => {
            handleChange(e);
          }}
          onKeyDown={handleKeyDown}
          // onPaste={handlePaste}
          end={
            <SearchIcon
              style={{ cursor: 'pointer' }}
              onClick={() => handleSearch()}
            />
          }
        />
      </Field>
    </>
  );
};

export default SearchInput;
