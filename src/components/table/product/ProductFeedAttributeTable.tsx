import React, { useState, useEffect } from 'react';
import {
  TableContainer,
  TableHolder,
  TableContainer as _TableContainer,
} from '../../UI-components/Table';
import { Col, Row } from '../../UI-components/Grid';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { Button } from '../../UI-components/Button';
import { Pagination } from '../../UI-components/Pagination';
import { DownIcon, LeftArrowIcon, RightArrowIcon } from '../../../utils/icons';
import { Link as _Link } from 'react-router-dom';
import { DataTable } from './DataTable';
import TopBar from '../../topbar/TopBarStyleTwo';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  getSortedRowModel
} from '@tanstack/react-table';
import {
  ProductFeedAttributesColumn,
  ProductFeedAttributesColumns as columns,
} from './Columns';
import { Container } from '../../UI-components/Container';
import { ProductDataAttrOutput } from '../../../gql/graphql';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { Span } from '@zendeskgarden/react-typography';
import { ProductFeedAttributeFilters } from '../../drawer/catalog-service/ProductFeedAttributeDrawer';
import UploadCSVModal from '../../modal/catalog-service/UploadCSVModal';
import { ProductFeedDrawer } from '../../drawer/catalog-service/ProductFeedDrawer';

export type ColumnSort = {
  id: string
  desc: boolean
}

export type SortingState = ColumnSort[]

export const ProductFeedAttributesTable = ({
  data,
  page,
  setPage,
  totalPage,
  searchContent,
  setSearchContent,
  count,
  filters,
  setFilters,
  handleSearch,
  searchType,
  setSearchType,
  refetch,
}: {
  data: ProductDataAttrOutput[];
  page?: number;
  setPage?: React.Dispatch<React.SetStateAction<number>>;
  totalPage: number;
  searchContent: string | undefined;
  setSearchContent: React.Dispatch<React.SetStateAction<string | undefined>>;
  count: number;
  filters: any;
  setFilters: any;
  handleSearch: any;
  searchType: string | undefined;
  setSearchType?: React.Dispatch<React.SetStateAction<string | undefined>>;
  refetch: any;
}) => {
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [visible, setVisible] = useState(false);
  const [btnsDrawerOpen, setBtnsDrawerOpen] = useState(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getSortedRowModel: getSortedRowModel(),
    state: {
      columnVisibility,
      rowSelection,
      sorting: sorting,
    },
    onSortingChange: setSorting,
  });
  const handleRowPerPage = (item: string) => {
    const pageSize: number = Number(item);
    setFilters((prev: any) => ({
      ...prev,
      rowsPerPage: pageSize,
      pageNumber: 1,
    }));
    table.setPageSize(pageSize);
  };
  useEffect(() => {
    table.setPageSize(Number(filters.rowsPerPage));
  }, []);

  const close = () => setVisible(false);

  return (
    <>
      <TopBar
        table={table}
        data={data}
        searchContent={searchContent}
        setSearchContent={setSearchContent}
        haveSearch={true}
        searchPlaceholder={'Search by Product Id/ Sku Id/ Product Name'}
        handleSearch={handleSearch}
        searchType={searchType}
        setSearchType={setSearchType}
        filters={filters}
        setFilters={setFilters}
        refetch={refetch}
        haveFilters={true}
        setIsOpen={setIsOpen}
        haveUpload={true}
        setUploadModal={setVisible}
        haveExport={true}
        haveGenerate={true}
        haveBtnsDrawer={true}
        setBtnsDrawerOpen={setBtnsDrawerOpen}
      />
      {visible && <UploadCSVModal close={close} />}
      <Container>
        <TableContainer>
          <TableHolder>
            <DataTable table={table} columns={columns} data={data} />
          </TableHolder>
          <div style={{ overflowX: 'clip' }}>
            <Row
              style={{
                height: `${baseTheme.components.dimension.width.base * 5}px`,
                marginTop: baseTheme.space.sm,
                backgroundColor: colors.white,
                paddingLeft: baseTheme.space.lg,
                paddingRight: baseTheme.space.lg,
              }}
              justifyContent="center"
              alignItems="center"
            >
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="center">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={filters.page <= 1 ? true : false}
                    onClick={() => {
                      setFilters((prev: any) => ({
                        ...prev,
                        page: prev.page - 1,
                      }));
                    }}
                  >
                    <Button.StartIcon>
                      <LeftArrowIcon />
                    </Button.StartIcon>
                    Previous
                  </Button>
                </Row>
              </Col>
              <Col textAlign="center" lg={2} md={2}>
                <Dropdown
                  onSelect={(item) => handleRowPerPage(item)}
                  onStateChange={(options) =>
                    Object.hasOwn(options, 'isOpen') &&
                    setRotated(options.isOpen)
                  }
                >
                  <Trigger>
                    <Button size="medium" isAction>
                      Row Per Page:
                      <Span style={{ paddingLeft: baseTheme.space.sm }}>
                        {table.getState().pagination.pageSize}
                      </Span>
                      <Button.EndIcon
                        isRotated={rotated}
                        style={{ marginLeft: 0 }}
                      >
                        <DownIcon />
                      </Button.EndIcon>
                    </Button>
                  </Trigger>
                  <Menu>
                    <Item value={20}>20</Item>
                    <Item value={50}>50</Item>
                    <Item value={100}>100</Item>
                  </Menu>
                </Dropdown>
              </Col>
              <Col lg={5} md={5}>
                <Row justifyContent="center" alignItems="center">
                  <Pagination
                    color={baseTheme.colors.deepBlue}
                    totalPages={Math.ceil(
                      count / table.getState().pagination.pageSize,
                    )}
                    pagePadding={2}
                    currentPage={filters.page}
                    onChange={(e) =>
                      setFilters((prev: any) => ({ ...prev, page: e }))
                    }
                  />
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="start" alignItems="end">
                  <Button size="medium" isAction>
                    {(filters.page - 1) * table.getState().pagination.pageSize +
                      1}
                    -
                    {count < filters.page * table.getState().pagination.pageSize
                      ? count
                      : filters.page *
                      table.getState().pagination.pageSize}{' '}
                    of {count}
                  </Button>
                </Row>
              </Col>
              <Col lg={1.5} md={1.5}>
                <Row justifyContent="end" alignItems="end">
                  <Button
                    style={{
                      maxWidth: baseTheme.components.dimension.width.base100,
                    }}
                    size="medium"
                    isAction
                    disabled={filters.page >= count - 1 ? true : false}
                    onClick={() =>
                      setFilters((prev: any) => ({
                        ...prev,
                        pageNumber: prev.page + 1,
                      }))
                    }
                  >
                    Next
                    <Button.EndIcon>
                      <RightArrowIcon />
                    </Button.EndIcon>
                  </Button>
                </Row>
              </Col>
            </Row>
          </div>
        </TableContainer>
      </Container>
      <ProductFeedAttributeFilters
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        filters={filters}
        setFilters={setFilters}
        reset={refetch}
      />
      <ProductFeedDrawer
        isOpen={btnsDrawerOpen}
        setIsOpen={setBtnsDrawerOpen}
        filters={filters}
        haveUpload={true}
        setUploadModal={setVisible}
        haveExport={true}
        haveGenerate={true}
      />
    </>
  );
};
