import React, { ReactNode, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import UpdateSkuTable from '../../../components/layouts/catalog-service/updateSku/UpdateSkuTable';
import { Button } from '../../../components/UI-components/Button';
import { DownIcon, FilterIcon, RefetchIcon } from '../../../utils/icons';
import SearchInput from '../../../components/search-input/SearchInput';
import { Row, Col } from '../../../components/UI-components/Grid';
import { Span } from '@zendeskgarden/react-typography';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import { useMutation, useQuery } from '@tanstack/react-query';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import UpdateSKUModal from '../../../components/modal/catalog-service/UpdateSKUModal';
import UpdateSkuFilter from '../../../components/drawer/catalog-service/UpdateSku/UpdateSkuFilter';
import { useProductContext } from '../ProductFilterContext';
import {
  UpdateSKUColumns,
  UpdateSKUTableColumns as columns,
} from '../../../components/table/product/Columns';
import {
  Autocomplete,
  Dropdown,
  Field,
  Item,
  Menu,
  Trigger,
} from '@zendeskgarden/react-dropdowns';
import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Checkbox, Fieldset, Label } from '@zendeskgarden/react-forms';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

const TableContainer = styled.div`
  overflow-x: auto;
  border-radius: ${baseTheme.components.dimension.width.zero};
  background-color: white;
  height: 78vh;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 74vh;
  }
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-top: ${(p) => p.theme.space.sm};
  padding-bottom: ${(p) => p.theme.space.sm};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;
const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

export interface FiltersType {
  page: number;
  size: number;
  old_sku?: string;
  new_sku?: string;
  filters_updated_at_from?: string;
  filters_updated_at_to?: string;
  filters_status?: number;
  filters_created_at_to?: string;
  filters_created_at_from?: string;
}

const UpdateSku = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<FiltersType>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    size: 20,
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentSearch, setCurrentSearch] = useState<string>();
  const [searchContent, setSearchContent] = useState<string | undefined>('');
  const [isOpen, setIsOpen] = useState(false);
  const [count, setCount] = useState<number>(0);
  const [stockData, setStockData] = useState<any[]>([]);
  const [rotated, setRotated] = useState<boolean | undefined>();
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const axios = useAxios();
  const addToast = useToast();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['update-sku-list'],
      queryFn: async (): Promise<any> => {
        return await axios.get(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/products/skus/updates`,
          {
            params: { ...filters },
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              admin_identifier: `${localStorage.getItem('username')}`,
            },
          },
        );
      },
      onError: (err: any) => {
        addToast('error', `${err?.message}`);
        setCurrentSearch('');
        setStockData([]);
      },
      onSuccess: (data) => {
        setCount(data.item_count);
        setStockData(data?.items);
      },
    });

  const handleRefetch = () => {
    refetch();
  };

  const hadleReset = () => {
    setFilters({
      page: 1,
      size: 20,
    });
  };

  const table = useReactTable({
    data: stockData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const { mutate, isLoading: isExportLoading } = useMutation(
    async () => {
      const response: Blob = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/products/skus/updates/export`,
        {
          params: {
            old_sku: filters.old_sku,
            filters_status: filters.filters_status,
            filters_created_at_from: filters.filters_created_at_from,
            filters_created_at_to: filters.filters_created_at_to,
            filters_updated_at_from: filters.filters_updated_at_from,
            filters_updated_at_to: filters.filters_updated_at_to,
            new_sku: filters.new_sku,
          },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
            admin_identifier: `${localStorage.getItem('username')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (error: any) => {
        addToast('error', `${error?.message}`);
      },
      onSuccess(data, response, context) {
        const blob = new Blob([data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'data.csv';
        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      },
    },
  );

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setPage(Number(queryParams.get('page')));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
      setPage(1);
    }
    if (Object.keys(filters).length > 2) {
      refetch();
    }
    table.toggleAllPageRowsSelected(false);
  }, [queryParams.get('page'), filters]);

  const { setHeaderInformation } = useAuth();
  useEffect(() => {
    setHeaderInformation({
      title: 'SKU Updater View',
      breadcrumbParent: 'Products',
    });
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
    table.setPageSize(20);
  }, []);
  return (
    <div>
      <TopBarDiv>
        <Row>
          <Col
            style={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <Button isAction onClick={() => setIsOpen(!isOpen)}>
              Update SKU
            </Button>
            <Button
              size="medium"
              isAction
              onClick={() => {
                setIsFilterOpen(!isFilterOpen);
              }}
            >
              <Button.StartIcon>
                <FilterIcon />
              </Button.StartIcon>
              <Span>Filter</Span>
            </Button>
            <Button size="medium" isAction onClick={handleRefetch}>
              <Button.StartIcon>
                <RefetchIcon />
              </Button.StartIcon>
              <Span>Refetch</Span>
            </Button>

            <Button
              isAction
              onClick={() => {
                mutate();
              }}
            >
              Export
            </Button>
          </Col>
        </Row>
      </TopBarDiv>
      <Container>
        <TableContainer>
          {isLoading || isRefetching || isFetching ? (
            <LazyLoading />
          ) : (
            <UpdateSkuTable
              data={stockData}
              count={count}
              filters={filters}
              setFilters={setFilters}
              page={page}
              setPage={setPage}
              table={table}
            />
          )}
        </TableContainer>
      </Container>
      {isOpen && <UpdateSKUModal visible={isOpen} setVisible={setIsOpen} />}
      {isFilterOpen && (
        <UpdateSkuFilter
          isOpen={isFilterOpen}
          setIsOpen={setIsFilterOpen}
          setFilters={setFilters}
          filters={filters}
          reset={hadleReset}
        />
      )}
    </div>
  );
};

export default UpdateSku;
