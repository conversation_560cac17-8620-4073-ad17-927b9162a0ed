import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { baseTheme, colors } from '../../../themes/theme';
import {
  StockAlertTableColumns as columns,
  StockAlertColumns,
} from '../../../components/table/product/Columns';

import {
  VisibilityState,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { TableHolder } from '../../../components/UI-components/Table';
import { DataTable } from '../../../components/table/product/DataTable';
import NothingToshow from '../../../components/UI-components/NothingToShow';
import { Button } from '../../../components/UI-components/Button';
import { Col, Row } from '@zendeskgarden/react-grid';
import SearchInput from '../../../components/search-input/SearchInput';
import { DownIcon, FilterIcon, RefetchIcon } from '../../../utils/icons';
import { Span } from '@zendeskgarden/react-typography';
import StockAlertFilterDrawer from '../../../components/drawer/catalog-service/StockAlertFilterDrawer';
import useAxios from '../../../hooks/useAxios';
import useToast from '../../../hooks/useToast';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@tanstack/react-query';
import StockAlertTable from '../../../components/layouts/catalog-service/StockAlertTable/StockAlertTable';
import LazyLoading from '../../../components/UI-components/LazyLoading';
import routes from '../../../constants/routes';
import { Dropdown, Item, Menu, Trigger } from '@zendeskgarden/react-dropdowns';
import { useScreenDefaultWidth } from '../../../hooks/useScreenSize';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';
import { useAuth } from '../../../components/providers/AuthProvider';

const TableContainer = styled.div`
  overflow-x: auto;
  border-radius: ${baseTheme.components.dimension.width.zero};
  background-color: white;
  height: 78vh;
  @media (max-height: ${baseTheme.breakpoints.md}) {
    height: 74vh;
  }
`;

const TopBarDiv = styled.div`
  background-color: ${colors.white};
  padding-bottom: ${(p) => p.theme.space.md};
  padding-left: ${(p) => p.theme.space.md};
  padding-right: ${(p) => p.theme.space.md};
`;

const Container = styled.div`
  padding: ${(p) => p.theme.space.md};
`;

export interface StockAlertFiltersType {
  page: number;
  size: number;
  sku?: string;
  product_name?: string;
  customer_name?: string;
  customer_email?: string;
  filters_created_at_to?: string;
  filters_created_at_from?: string;
}

const StockAlert = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const { setHeaderInformation } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [rowSelection, setRowSelection] = useState({});
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<StockAlertFiltersType>({
    page:
      Number(queryParams.get('page')) > 0 ? Number(queryParams.get('page')) : 1,
    size: 20,
  });
  const [searchName, setSearchName] = useState<string>('');
  const [searchSku, setSearchSku] = useState<any>();
  const [searchType, setSearchType] = useState<string>('product_name');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentSearch, setCurrentSearch] = useState<string>();
  const [searchContent, setSearchContent] = useState<string | undefined>('');
  const [count, setCount] = useState<number>(0);
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const axios = useAxios();
  const addToast = useToast();
  const [rotatedType, setRotatedType] = useState<boolean>();

  const isSmallScreen = useScreenDefaultWidth();

  const { isLoading, isError, error, data, isRefetching, isFetching, refetch } =
    useQuery({
      queryKey: ['stock-alert-list'],
      queryFn: async (): Promise<any> => {
        const params = {
          product_name: searchName ? searchContent : filters.product_name,
          sku: searchSku ? searchSku : filters.sku,
          customer_email: filters.customer_email ?? undefined,
          customer_name: filters.customer_name ?? undefined,
          filters_created_at_from: filters.filters_created_at_from ?? undefined,
          filters_created_at_to: filters.filters_created_at_to ?? undefined,
        };
        return await axios.get(
          `${krakendPaths.CATALOG_URL}/admin-api/v1/stock-alert`,
          {
            params: params,
            headers: {
              Authorization: `Bearer ${localStorage.getItem('api-token')}`,
              'x-api-key': `${constants.CATALOG_KEY}`,
              admin_identifier: `${localStorage.getItem('username')}`,
            },
          },
        );
      },
      onError: (err) => {
        addToast('error', 'Error Occured');
        setCurrentSearch('');
      },
      onSuccess: (data) => {
        setCount(data.item_count);
      },
    });

  const table = useReactTable({
    data: data?.items,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      columnVisibility,
      rowSelection,
    },
  });

  const { mutate, isLoading: isExportLoading } = useMutation(
    async () => {
      const response: Blob = await axios.get(
        `${krakendPaths.CATALOG_URL}/admin-api/v1/products/stock-alert/export`,
        {
          params: { ...filters },
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
            admin_identifier: `${localStorage.getItem('username')}`,
          },
        },
      );
      return response;
    },
    {
      onError: (error: any) => {
        addToast('error', `${error?.message}`);
      },
      onSuccess(data) {
        const blob = new Blob([data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'data.csv';
        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      },
    },
  );

  useEffect(() => {
    if (Object.keys(filters).length > 2) {
      refetch();
    }
  }, [filters]);

  const handleRefetch = () => {
    setCurrentSearch('');
    setSearchContent('');
    setSearchName('');
    setSearchSku('');
    refetch();
  };

  const handleSearch = () => {
    // console.log('searchContent: ', searchContent);
    if (searchContent) {
      setPage(1);
      if (searchType == 'sku') {
        setSearchName('');
        setSearchSku(searchContent);
      } else {
        setSearchSku(undefined);
        setSearchName(searchContent.trim());
      }
      setCurrentSearch(searchContent.trim());
    }
  };

  useEffect(() => {
    if (
      Number(queryParams.get('page')) !== filters.page &&
      Number(queryParams.get('page')) !== 0
    ) {
      setFilters((prev) => ({
        ...prev,
        page: Number(queryParams.get('page')),
      }));
    } else if (Number(queryParams.get('page')) === 0) {
      setFilters((prev: any) => ({ ...prev, page: 1 }));
    }
  }, [queryParams.get('page')]);

  useEffect(() => {
    if (searchName !== '' || searchSku !== undefined) {
      refetch();
    }
  }, [searchName, searchSku]);

  useEffect(() => {
    setHeaderInformation({
      title: 'Customer signup for stock alert',
      breadcrumbParent: '',
    });
    queryParams.set('page', filters.page > 0 ? filters.page.toString() : '1');
    navigate({ search: queryParams.toString() });
  }, []);

  const searchOptions = [
    {
      label: 'Product Name',
      value: 'product_name',
    },
    {
      label: 'SKU',
      value: 'sku',
    },
  ];
  return (
    <div>
      <TopBarDiv>
        <Row>
          <Col size={6}>
            <Row justifyContent="between" alignItems="center">
              <Col>
                <Row alignItems="center">
                  <Col size={6.5}>
                    <Row>
                      <SearchInput
                        isLabelHidden
                        searchContent={searchContent}
                        setSearchContent={setSearchContent}
                        placeholder="Search by Name/sku"
                        handleSearch={handleSearch}
                        label=""
                        width="100%"
                      />
                    </Row>
                  </Col>
                  {searchOptions && (
                    <Col offset={0.5} size={2}>
                      <>
                        <Dropdown
                          onSelect={(item) => setSearchType(item)}
                          onStateChange={(options) =>
                            Object.hasOwn(options, 'isOpen') &&
                            setRotatedType(options.isOpen)
                          }
                        >
                          <Trigger>
                            <Button
                              style={{
                                transform: isSmallScreen
                                  ? 'translateX(-30px)'
                                  : 'translateX(0)',
                              }}
                              isPrimary
                            >
                              {searchOptions.map((option: any) => {
                                return option.value === searchType
                                  ? option.label
                                  : null;
                              })}
                              <Button.EndIcon isRotated={rotatedType}>
                                <DownIcon
                                  style={{
                                    height: baseTheme.iconSizes.md,
                                    width: baseTheme.iconSizes.md,
                                  }}
                                />
                              </Button.EndIcon>
                            </Button>
                          </Trigger>
                          <Menu
                            style={{
                              width:
                                baseTheme.components.dimension.width.base200,
                              transform: 'translateX(4px)',
                              borderRadius: baseTheme.borderRadii.lg,
                            }}
                          >
                            {searchOptions.map((option: any) => (
                              <Item key={option.value} value={option.value}>
                                {option.label}
                              </Item>
                            ))}
                          </Menu>
                        </Dropdown>
                      </>
                    </Col>
                  )}
                </Row>
              </Col>
            </Row>
          </Col>
          <Col
            style={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <>
              <Button
                size="medium"
                isAction
                onClick={() => {
                  setIsFilterOpen(!isFilterOpen);
                }}
              >
                <Button.StartIcon>
                  <FilterIcon />
                </Button.StartIcon>
                <Span>Filter</Span>
              </Button>
              <Button size="medium" isAction onClick={handleRefetch}>
                <Button.StartIcon>
                  <RefetchIcon />
                </Button.StartIcon>
                <Span>Refetch</Span>
              </Button>
              <Button
                isAction
                onClick={() => {
                  mutate();
                }}
              >
                Export
              </Button>
            </>
          </Col>
        </Row>
        <Row></Row>
      </TopBarDiv>
      <Container>
        <TableContainer>
          {isLoading || isRefetching || isFetching ? (
            <LazyLoading />
          ) : (
            <StockAlertTable
              data={data?.items}
              count={count}
              filters={filters}
              setFilters={setFilters}
              page={page}
              setPage={setPage}
              table={table}
            />
          )}
        </TableContainer>
      </Container>
      {isFilterOpen && (
        <StockAlertFilterDrawer
          isOpen={isFilterOpen}
          setIsOpen={setIsFilterOpen}
          setFilters={setFilters}
          filters={filters}
          refetch={refetch}
        />
      )}
    </div>
  );
};

export default StockAlert;
