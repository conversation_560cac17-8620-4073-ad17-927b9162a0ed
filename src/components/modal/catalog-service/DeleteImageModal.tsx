import React from 'react';
import { <PERSON><PERSON> } from '@zendeskgarden/react-buttons';
import {
  Modal,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Spinner } from '@zendeskgarden/react-loaders';

interface DeleteImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  isLoading?: boolean;
}

const DeleteImageModal: React.FC<DeleteImageModalProps> = ({
  isOpen,
  onClose,
  onDelete,
  isLoading = false,
}) => {
  if (!isOpen) return null;
  
  return (
    <Modal onClose={onClose}>
      <Header tag="h2" isDanger>
        Delete Category Image
      </Header>
      <Body>Are you sure you want to delete this category image?</Body>
      <Footer>
        <FooterItem>
          <Button onClick={onClose} isBasic>
            Cancel
          </Button>
        </FooterItem>
        <FooterItem>
          <Button
            isPrimary
            isDanger
            onClick={onDelete}
            disabled={isLoading}
          >
            {isLoading ? <Spinner /> : 'Delete'}
          </Button>
        </FooterItem>
      </Footer>
      <Close aria-label="Close modal" />
    </Modal>
  );
};

export default DeleteImageModal;