import React, { useEffect, useRef, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import {
  Dropdown,
  Item,
  Menu,
  Trigger,
  Label as DropLabel,
  Field as DropField,
  Select,
  Autocomplete,
} from '@zendeskgarden/react-dropdowns';
import { Button } from '../../UI-components/Button';
import {
  Field,
  Input,
  Fieldset,
  Label,
  Checkbox,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import { Span } from '@zendeskgarden/react-typography';
import styled, { css } from 'styled-components';
import SearchInput from '../../search-input/SearchInput';
import GenericSelectItem from '../../dropdown/GenericSelectItem';
import { IDropdownItem } from '../../../types/types';
import { DownIcon, Plus, PlusIcon, SearchIcon, XCircle } from '../../../utils/icons';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';
import useToast from '../../../hooks/useToast';
import routes from '../../../constants/routes';
import { Spinner } from '@zendeskgarden/react-loaders';
import { MenuItem } from '../../../pages/catalog-service/CategoryDropDown';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const Row = styled(_Row)`
  margin: 20px 0px;
`;
const DropdownContainer = styled.div``;

const DropdownList = styled.div`
  top: 100%;
  left: 0;
  width: 100%;
  background-color: #fff;
  border: 1px solid #ccc;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

interface OptionProps {
  isSelected?: boolean;
}

const Option = styled.label<OptionProps>`
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;

  &:hover {
    background-color: #f0f0f0;
  }
  ${(props) =>
    props.isSelected &&
    css`
      cursor: not-allowed;
      pointer-events: none;
    `}
`;

const SelectedOptions = styled.div`
  display: flex;
  flex-wrap: wrap;
`;

const capitalizeFirstLetter = (item: string) => {
  return item.charAt(0).toUpperCase() + item.slice(1);
};

const isValidString = (str: string) => {
  const regex = /^[a-zA-Z0-9\s()]+$/;
  return str.trim().length > 0 && regex.test(str);
};

const DENTAL_BRANDS_ID = 1971;

const AddNewCategory = ({
  visible,
  setVisible,
}: {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [categoryName, setCategoryName] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<{
    name: string;
    id: number;
  }>();
  const wordLimit = 40;
  const [searchValue, setSearchValue] = useState<string>();
  const [categoryData, setCategoryData] = useState<
    { name: string; id: number }[]
  >([]);
  const [nameError, setNameError] = useState<string>('');
  const { contextProdData, setContextProdData, category } = useProductContext();
  const [show, setShow] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLUListElement>(null);

  const addToast = useToast();

  const [categName, setCategName] = useState('');

  const { data: treeData, refetch: refetchCategoryData } = useQuery({
    queryKey: ['category-tree-data'],
    queryFn: async () => {
      const res = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories/tree`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category/category-tree/`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );
      return res?.data?.data?.categoryList as MenuItem[];
    },
  });

  const handleSave = () => {
    if (!nameError && isValidString(categoryName)) {
      if (selectedCategory?.id === DENTAL_BRANDS_ID) {
        return addToast(
          'info',
          'Unable to create brand, use Brand management section.',
        );
      } else {
        mutate();
      }
    } else {
      addToast('warning', 'Category name is invalid or empty.');
    }
  };

  const findCategoryPath = (
    categories: MenuItem[] | undefined,
    categoryId: number,
    currentPath = '',
  ) => {
    if (categories)
      for (const category of categories) {
        const newPath = currentPath
          ? `${currentPath}/${category.name}`
          : category.name;

        if (category.id === categoryId) {
          return newPath as string;
        }

        if (category.children) {
          const foundPath: string = findCategoryPath(
            category.children,
            categoryId,
            newPath,
          );
          if (foundPath) {
            return foundPath as string;
          }
        }
      }
    return '';
  };

  const fetchCatagoryList = async ({ pageParam = 1, search = '' }) => {
    try {
      const response = await axios.get(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories?page_no=${pageParam}&category_name=${search}`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category?page_no=${pageParam}&category_name=${search}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  };

  const {
    data,
    isSuccess,
    isLoading,
    isFetching,
    fetchNextPage,
    hasNextPage,
    error,
  } = useInfiniteQuery({
    queryKey: ['category-list', categName],
    queryFn: ({ pageParam = 1 }) =>
      fetchCatagoryList({ pageParam, search: categName }),
    getNextPageParam: (lastPage: any) => {
      const currentPage = lastPage.page_no;
      const totalPages = Math.ceil(lastPage.item_count / lastPage.page_size);
      if (currentPage < totalPages) {
        return currentPage + 1;
      } else {
        return undefined;
      }
    },
  });

  useEffect(() => {
    if (error) {
      addToast('error', 'Failed to fetch categories.');
    }
  }, [error, addToast]);

  useEffect(() => {
    if (data) {
      const value = data.pages.flatMap((page: any) =>
        page.items.map((d: MenuItem) => ({ name: d.name, id: d.id })),
      );
      setCategoryData(value);
    }
  }, [data]);

  const close = () => {
    setVisible(false);
  };

  const { mutate, isLoading: isUpdateLoading } = useMutation({
    mutationFn: async () => {
      const response = await axios.post(
        // `${krakendPaths.CATALOG_URL}/admin-api/v1/categories`,
        `${constants.CATALOG_URL}/v1/catalog-admin/category`,
        {
          name: categoryName,
          parent_id: selectedCategory?.id ? selectedCategory.id : 2,
          status: true,
        },

        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': `${constants.CATALOG_KEY}`,
          },
        },
      );

      return response as any;
    },
    onSuccess() {
      addToast('success', 'Category added successfully');
    },
    onError(err) {
      addToast('error', (err as any)?.response?.data?.message);
    },
    onSettled(data, error, variables, context) {
      close();
      setCategoryName('');
      setSelectedCategory(undefined);
    },
  });
  return (
    <Modal
      isLarge
      style={{
        borderRadius: '15px',
        height: `${baseTheme.components.dimension.height.base * 80}px`,
      }}
      onClose={() => setVisible(false)}
    >
      <Header
        tag="h2"
        style={{
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          color: 'white',
          justifyContent: 'center',
          display: 'flex',
        }}
      >
        Add New Category
      </Header>
      <Body style={{ position: 'relative' }}>
        <Row>
          <Col>
            <Field>
              <Label>Name</Label>
              <Input
                value={categoryName}
                onChange={(e) => {
                  const newValue = e.target.value;
                  const onlyNumbersRegex = /^\d+$/;
                  if (onlyNumbersRegex.test(newValue)) {
                    setNameError('Category Name should not be an integer!');
                  } else if (!isValidString(newValue)) {
                    setNameError('Category Name contains invalid characters!');
                  } else {
                    setNameError('');
                  }
                  if (newValue.length <= wordLimit) {
                    setCategoryName(capitalizeFirstLetter(newValue));
                  }
                  // setCategoryName(capitalizeFirstLetter(e.target.value));
                }}
                placeholder="Category Name"
              />
            </Field>
            {nameError && <div style={{ color: 'red' }}>{nameError}</div>}
          </Col>
        </Row>
        <Row>
          <Col style={{ marginBottom: '60px' }}>
            <Dropdown
              inputValue={categName}
              selectedItem={selectedCategory}
              onSelect={(i) => {
                setSelectedCategory(i);
              }}
              onInputValueChange={(value) => setCategName(value)}
              downshiftProps={{
                itemToString: (item: { name: string; id: number }) => {
                  return item;
                },
              }}
            >
              <DropField>
                <Label>Parent Category</Label>
                <Autocomplete start={<SearchIcon />}>
                  {selectedCategory?.name}
                </Autocomplete>
              </DropField>
              <Menu>
                {categoryData.length ? (
                  categoryData.map((option, index) => {
                    const categoryPath = findCategoryPath(treeData, option.id);
                    return (
                      <Item key={index} value={option}>
                        <Row>
                          <Col>{`${option.name} (ID: ${option.id})`}</Col>
                          <Col size={7}>
                            <span
                              style={{ fontSize: '12px' }}
                            >{`${categoryPath}`}</span>
                          </Col>
                        </Row>
                      </Item>
                    );
                  })
                ) : (
                  <Item disabled>No matches found</Item>
                )}
                {(isFetching || isLoading) && (
                  <Row justifyContent="center">
                    <Spinner />
                  </Row>
                )}
                {hasNextPage &&
                  categoryData.length > 0 &&
                  !(isFetching || isLoading) && (
                    <Row justifyContent="center">
                      <Button
                        isBasic
                        onClick={() => {
                          fetchNextPage();
                        }}
                        style={{ marginBottom: `${baseTheme.paddings.base}` }}
                      >
                        Load More
                      </Button>
                    </Row>
                  )}
              </Menu>
            </Dropdown>
          </Col>
        </Row>
        <Row
          style={{
            display: 'flex',
            justifyContent: 'end',
            position: 'absolute',
            bottom: '0',
            right: '5%',
          }}
        >
          <Button
            isPrimary
            isOrange
            onClick={() => {
              if (!nameError && isValidString(categoryName)) {
                mutate();
              } else {
                addToast('warning', 'Category name is invalid or empty.');
              }
            }}
          >
            {isUpdateLoading ? <Spinner /> : 'Save'}
          </Button>
        </Row>
      </Body>
      {/* <Footer>
              <FooterItem>
                <Button
                  isPrimary
                  isOrange
                  onClick={() => {
                    if( !nameError && isValidString(categoryName) ){
                      mutate();
                    }else {
                      addToast('warning', 'Category name is invalid or empty.');
                    }
                  }}
                >
                  {isUpdateLoading ? <Spinner /> : 'Save'}
                </Button>
              </FooterItem>
            </Footer> */}
      <Close style={{ color: 'white' }} aria-label="Close modal" />
    </Modal>
  );
};

export default AddNewCategory;
