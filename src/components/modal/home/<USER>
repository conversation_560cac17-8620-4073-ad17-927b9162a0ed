import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Row, Col } from '@zendeskgarden/react-grid';
import {
  <PERSON><PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import {
  Checkbox,
  Field,
  FileUpload as _FileUpload,
  Input,
  Label,
  Message,
} from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import {
  Combobox,
  Field as DropField,
  IComboboxProps,
  Label as DropLabel,
  Option,
} from '@zendeskgarden/react-dropdowns.next';
import { debounce } from 'lodash';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import ProductDropdown from '../../dropdown/catalog-service/ProductDropdown';
import useAxios from '../../../hooks/useAxios';
import routes from '../../../constants/routes';
import useToast from '../../../hooks/useToast';
import { useMutation } from '@tanstack/react-query';
import { useParams } from 'react-router-dom';
import {
  CrossIcon,
  DeleteIcon,
  DropIcon,
  Plus,
  UploadPlus,
  XCircle,
} from '../../../utils/icons';
import { useDropzone } from 'react-dropzone';
import { Spinner } from '@zendeskgarden/react-loaders';
import { IconButton } from '../../UI-components/IconButton';
import constants from '../../../constants';
import krakendPaths from '../../../constants/krakendPaths';

const StyledFileUpload = styled(_FileUpload)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: ${baseTheme.components.dimension.width.base150};
  height: ${baseTheme.components.dimension.width.base150};
  border: 2px dashed ${baseTheme.colors.grey};
  border-radius: 8px;
  background-color: ${({ isDragging }) =>
    isDragging ? baseTheme.colors.lightGrey : 'transparent'};
`;

const FilePreview = styled.div<{ src: string }>`
  width: ${baseTheme.components.dimension.width.base150};
  height: ${baseTheme.components.dimension.width.base150};
  border: 2px dashed ${baseTheme.colors.grey};
  border-radius: 8px;
  background-image: ${(p) => `url("${p.src}")`};
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  margin-top: 20px;
`;
const FileDisplay = styled.div<{ src: string }>`
  width: 100%;
  height: ${baseTheme.components.dimension.width.base250};
  background-image: ${(p) => `url("${p.src}")`};
  background-repeat: no-repeat;
  background-size: cover;
`;

const ModalBody = styled(Body)`
  display: flex;
  flex-direction: column;

  gap: 6px;
  > img {
    max-width: 100%;
    max-height: 100%;
    margin: auto;
  }
`;

interface MediaUploadProps {
  close: () => void;
  onUpload: (url: string) => void;
  existingImgSrc?: string;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  close,
  onUpload,
  existingImgSrc,
}) => {
  const axios = useAxios();
  const addToast = useToast();
  const { id } = useParams();
  const [prodId, setProdId] = useState<number | undefined>(undefined);

  const [file, setFile] = useState<File>();

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const newFiles = acceptedFiles.map((acceptedFile) => acceptedFile);

        setFile(acceptedFiles[0]);
      }
    },
    [file],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: { 'image/*': ['.jpeg', '.png', '.jpg', '.mp4'] },
    onDrop,
  });

  const { mutate: uploadMutation, isLoading } = useMutation(
    async () => {
      const formData = new FormData();

      formData.append('files', file as any);

      // console.log("formData : " , formData);

      const url: any = await axios.post(
        `${krakendPaths.INTERFACE_URL}/admin-api/v1/media-upload`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('api-token')}`,
            'x-api-key': 'dentalkart101',
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      return url?.uploaded_url as string;
    },
    {
      onError: (err: any) => {
        // console.log(err);
        if (err?.statusCode === 413) {
          addToast('warning', `Image should be below 200kb.`);
        } else {
          addToast('error', `${err?.message}`);
        }
        // refetch();
      },
      onSuccess(url: string) {
        console.log('url recieved', url);
        if (url) {
          // console.log('upload image/video data: ', data);
          addToast('success', 'Successfully uploaded the file');
          onUpload(url);
          close();
        }
      },
    },
  );
  const handleUpload = () => {
    console.log(file);

    uploadMutation();
  };

  //   useEffect(() => {
  //     const metaData = detectBrowser();
  //     setUserData(metaData);
  //   }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files && event.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  return (
    <Modal isLarge onClose={close}>
      <Header
        tag="h2"
        style={{
          display: 'flex',
          color: '#FFF',
          fontSize: '20px',
          backgroundColor: `${baseTheme.colors.deepBlue}`,
          justifyContent: 'center',
        }}
      >
        Upload Image
      </Header>
      <ModalBody>
        {file ? (
          <img src={URL.createObjectURL(file)} />
        ) : // <FileDisplay src={URL.createObjectURL(file)} />
        existingImgSrc ? (
          <img src={existingImgSrc} />
        ) : (
          <></>
        )}

        <Field>
          {!file && (
            <StyledFileUpload {...getRootProps()} isDragging={isDragActive}>
              {isDragActive ? <span> drag and drop here</span> : <UploadPlus />}
              <Input {...getInputProps()} />
            </StyledFileUpload>
          )}
          {file && (
            <Row justifyContent="between">
              <FilePreview src={URL.createObjectURL(file)}>
                <IconButton
                  onClick={() => {
                    setFile(undefined);
                  }}
                  style={{ position: 'absolute', top: 10, right: 10 }}
                >
                  <CrossIcon />
                </IconButton>
              </FilePreview>
            </Row>
          )}
          <div style={{ float: 'right' }}>
            <Button onClick={handleUpload} isPrimary>
              {isLoading ? <Spinner /> : 'Upload'}
            </Button>
          </div>
        </Field>
      </ModalBody>

      <Close aria-label="Close modal" style={{ color: 'white' }} />
    </Modal>
  );
};

export default MediaUpload;
