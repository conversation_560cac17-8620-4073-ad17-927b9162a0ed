{"name": "admin-section", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "compile": "graphql-codegen", "watch": "graphql-codegen -w"}, "dependencies": {"@apollo/client": "^3.7.15", "@radix-ui/react-dropdown-menu": "^2.0.5", "@tanstack/react-query": "^4.29.12", "@tanstack/react-query-devtools": "^4.29.12", "@tanstack/react-table": "^8.9.1", "@tinymce/tinymce-react": "^4.3.2", "@types/crypto-js": "^4.1.3", "@types/lodash": "^4.14.197", "@types/react-timeago": "^4.1.3", "@types/styled-components": "^5.1.26", "@zendeskgarden/react-accordions": "^8.67.0", "@zendeskgarden/react-avatars": "^8.69.3", "@zendeskgarden/react-breadcrumbs": "^8.67.0", "@zendeskgarden/react-chrome": "^8.67.0", "@zendeskgarden/react-colorpickers": "^8.73.0", "@zendeskgarden/react-datepickers": "^8.68.0", "@zendeskgarden/react-dropdowns": "^8.67.0", "@zendeskgarden/react-dropdowns.next": "^8.73.1", "@zendeskgarden/react-forms": "^8.67.0", "@zendeskgarden/react-grid": "^8.67.0", "@zendeskgarden/react-loaders": "^8.69.1", "@zendeskgarden/react-modals": "^8.67.0", "@zendeskgarden/react-notifications": "^8.67.0", "@zendeskgarden/react-pagination": "^8.67.0", "@zendeskgarden/react-tables": "^8.67.0", "@zendeskgarden/react-tabs": "^8.67.0", "@zendeskgarden/react-tags": "^8.67.0", "@zendeskgarden/react-theming": "^8.67.0", "@zendeskgarden/react-tooltips": "^8.67.0", "@zendeskgarden/react-typography": "^8.67.0", "axios": "^1.4.0", "chart.js": "^4.4.0", "class-variance-authority": "^0.6.0", "clsx": "^1.2.1", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "graphql": "^16.6.0", "jodit-react": "^5.2.19", "lib": "^5.0.1", "lib-utils-ts": "^2.0.4-stable", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.224.0", "moment-timezone": "^0.5.45", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.44.3", "react-quill": "^2.0.0", "react-router-dom": "^6.11.2", "react-timeago": "^7.1.0", "tailwind-merge": "^1.12.0", "tailwindcss-animate": "^1.0.5", "tinymce": "^5.10.8", "vite-plugin-svgr": "^3.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@graphql-codegen/cli": "^4.0.1", "@graphql-codegen/client-preset": "^4.0.0", "@types/react": "^18.0.28", "@types/react-beautiful-dnd": "^13.1.7", "@types/react-csv": "^1.1.9", "@types/react-dom": "^18.0.11", "@types/react-timeago": "^4.1.3", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^5.1.3", "vite": "^4.3.2"}, "description": "", "main": "tailwind.config.js", "keywords": [], "author": "", "license": "ISC"}