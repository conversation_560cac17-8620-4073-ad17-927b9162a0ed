import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { Row as _Row, Col } from '@zendeskgarden/react-grid';
import {
  Mo<PERSON>,
  Header,
  Body,
  Footer,
  FooterItem,
  Close,
} from '@zendeskgarden/react-modals';
import { Button } from '../../UI-components/Button';
import { Checkbox, Field, Input, Label } from '@zendeskgarden/react-forms';
import { baseTheme } from '../../../themes/theme';
import {
  Combobox,
  Field as DropField,
  IComboboxProps,
  Label as DropLabel,
  Option,
} from '@zendeskgarden/react-dropdowns.next';
import { debounce } from 'lodash';
import styled from 'styled-components';
import { useProductContext } from '../../../pages/catalog-service/ProductFilterContext';
import ProductDropdown from '../../dropdown/catalog-service/ProductDropdown';
import useToast from '../../../hooks/useToast';

interface AdvanceInventoryProps {
  visible: boolean;
  setVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const Row = styled(_Row)`
  margin-bottom: 20px;
`;


const AdvanceInventory: React.FC<AdvanceInventoryProps> = ({
  visible,
  setVisible,
}) => {
  const [visibleInventory, setVisibleInventory] = useState(false);
  const { contextProdData, setContextProdData, setContextUpdateProdData } =
    useProductContext();

  const handleAdvanceInvent = () => {
    setVisibleInventory(!visibleInventory);
  };

  const updateContextData = (attribute: string, value: any) => {
    setContextProdData((prevState) => ({
      ...prevState,
      inventory_details: {
        ...prevState.inventory_details,
        [attribute]: value,
      },
    }));
    setContextUpdateProdData((prevState) => ({
      ...prevState,
      inventory_details: {
        ...prevState.inventory_details,
        [attribute]: value,
      },
    }));
  };

  // Backorders
  const [backorder, setBackOrder] = useState<string | undefined>('No Backorders');
  const [inputBackOrders, setInputBackOrders] = useState('');
  const handleSelectBackOrders = (item: any) => {
    // if (item === 'No Backorders') {
    //   setBackOrder(undefined);
    //   // updateContextData('backorders', undefined);
    // } else {
      setBackOrder(item);
      // updateContextData('backorders', item === 'True' ? true : false);
    // }
  };
  const handleInputBackOrdersChange = (value: any) => {
    setInputBackOrders(value);
  };

  useEffect(() => {
    if (contextProdData?.inventory_details?.backorders != undefined) {
      setBackOrder(
        contextProdData?.inventory_details?.backorders === true
          ? 'Allow Qty Below 0'
          : 'No Backorders',
      );
    }
  }, [contextProdData]);

   // Min and Max Quantity Validation
  const [minSaleQty, setMinSaleQty] = useState<number | null>(null);
  const [maxSaleQty, setMaxSaleQty] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (contextProdData?.inventory_details?.min_sale_qty != undefined) {
      setMinSaleQty(contextProdData.inventory_details.min_sale_qty);
    }
    if (contextProdData?.inventory_details?.max_sale_qty != undefined) {
      setMaxSaleQty(contextProdData.inventory_details.max_sale_qty);
    }
  }, [contextProdData]);

  useEffect(() => {
    if (
      minSaleQty !== null && minSaleQty > 0 &&
      maxSaleQty !== null && maxSaleQty > 0 &&
      maxSaleQty < minSaleQty
    ) {
      setError('Maximum quantity should be greater than minimum quantity');
    } else {
      setError(null);
    }
  }, [minSaleQty, maxSaleQty]);

  const handleMinSaleQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value !== '' ? parseInt(e.target.value, 10) : null;
    setMinSaleQty(value);
    // updateContextData('min_sale_qty', value);
  };

  const handleMaxSaleQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value !== '' ? parseInt(e.target.value, 10) : null;
    setMaxSaleQty(value);
    // updateContextData('max_sale_qty', value);
  };

  const addToast = useToast();

  const handleSave = ()=>{ 
    if(error){
      addToast('error', 'Maximum quantity should be greater than minimum quantity.')
    }else{
      updateContextData('min_sale_qty', minSaleQty);
      updateContextData('max_sale_qty', maxSaleQty);
      updateContextData('backorders', backorder === 'Allow Qty Below 0' ? true : backorder === 'No Backorders' ? false : undefined);
      setVisible(false);
    }
  }

  return (
    <Row>
      <Col textAlign="center">
        {/* <Button onClick={() => setVisible(true)}>Open modal</Button> */}
        {visible && (
          <Modal onClose={() => setVisible(false)}>
            <Header
              tag="h2"
              style={{
                display: 'flex',
                color: '#FFF',
                fontSize: '20px',
                backgroundColor: `${baseTheme.colors.deepBlue}`,
                justifyContent: 'center',
              }}
            >
              Advanced Inventory
            </Header>
            <Body>
              <Row>
                <Col>
                  <Label>Minimum Qty Allowed in Shopping Cart (Global)</Label>
                  <Input
                    type="number"
                    value={minSaleQty ?? ''}
                    onChange={handleMinSaleQtyChange}
                  />
                </Col>
              </Row>
              <Row>
                <Col>
                  <Label>Maximum Qty Allowed in Shopping Cart (Global)</Label>
                  <Input
                    type="number"
                    value={maxSaleQty ?? ''}
                    onChange={handleMaxSaleQtyChange}
                  />
                  {error && <div style={{ color: 'red' }}>{error}</div>}
                </Col>
              </Row>
              <Row>
                <Col>
                  <Label>Backorders</Label>
                  <ProductDropdown
                    options={['Allow Qty Below 0', 'No Backorders']}
                    selectedItem={backorder}
                    inputValue={inputBackOrders}
                    onSelect={handleSelectBackOrders}
                    onInputValueChange={handleInputBackOrdersChange}
                  />
                </Col>
              </Row>
            </Body>
            <Footer>
              <FooterItem>
                <Button onClick={handleSave} isPrimary isOrange>
                  Done
                </Button>
              </FooterItem>
            </Footer>
            <Close aria-label="Close modal" style={{ color: 'white' }} />
          </Modal>
        )}
      </Col>
    </Row>
  );
};

export default AdvanceInventory;
